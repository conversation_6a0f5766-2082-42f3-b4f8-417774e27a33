"""
Generic metrics utilities for structured data extraction.

Provides customizable metrics collection and reporting for any extraction workflow.
"""
from __future__ import annotations

import json
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable, Union

# Simple ANSI color helpers
_COLOR = {
    "reset": "\033[0m",
    "bold": "\033[1m",
    "blue": "\033[34m",
    "cyan": "\033[36m",
    "green": "\033[32m",
    "magenta": "\033[35m",
    "yellow": "\033[33m",
}

def _c(text: str, color: str) -> str:
    return f"{_COLOR.get(color, '')}{text}{_COLOR['reset']}"


class BaseMetricsCollector(ABC):
    """Base class for metrics collection and reporting."""

    def __init__(self, name: str = "extraction"):
        self.name = name
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None

    def start_timing(self) -> None:
        """Start timing the extraction process."""
        self.start_time = time.time()

    def end_timing(self) -> None:
        """End timing the extraction process."""
        self.end_time = time.time()

    def get_total_time(self) -> float:
        """Get total processing time in seconds."""
        if self.start_time is None or self.end_time is None:
            return 0.0
        return self.end_time - self.start_time

    @abstractmethod
    def collect_metrics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Collect metrics from extraction results."""
        pass

    @abstractmethod
    def format_summary(self, metrics: Dict[str, Any]) -> str:
        """Format metrics into a human-readable summary."""
        pass


class StandardMetricsCollector(BaseMetricsCollector):
    """Standard metrics collector for token usage and processing times."""

    def __init__(self, name: str = "extraction", cost_per_input_token: float = 0.0, cost_per_output_token: float = 0.0):
        super().__init__(name)
        self.cost_per_input_token = cost_per_input_token
        self.cost_per_output_token = cost_per_output_token


    def collect_metrics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Collect standard metrics from extraction results."""
        if not results:
            return {"total_items": 0, "successful_items": 0, "failed_items": 0}

        successful = [r for r in results if not r.get("error")]
        failed = [r for r in results if r.get("error")]

        # Token metrics
        total_input_tokens = sum(r.get("input_tokens", 0) for r in results)
        total_output_tokens = sum(r.get("output_tokens", 0) for r in results)
        avg_input_tokens = total_input_tokens / len(results) if results else 0
        avg_output_tokens = total_output_tokens / len(results) if results else 0

        # Cost calculation
        total_cost = (total_input_tokens * self.cost_per_input_token +
                     total_output_tokens * self.cost_per_output_token)

        # Processing time metrics
        processing_times = [r.get("processing_time", 0.0) for r in results if r.get("processing_time")]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0.0

        return {
            "extraction_name": self.name,
            "total_time": self.get_total_time(),
            "total_items": len(results),
            "successful_items": len(successful),
            "failed_items": len(failed),
            "success_rate": len(successful) / len(results) if results else 0.0,
            "total_input_tokens": total_input_tokens,
            "total_output_tokens": total_output_tokens,
            "total_tokens": total_input_tokens + total_output_tokens,
            "avg_input_tokens": round(avg_input_tokens, 2),
            "avg_output_tokens": round(avg_output_tokens, 2),
            "avg_processing_time": round(avg_processing_time, 2),
            "total_cost": round(total_cost, 6),
            "errors": [r.get("error") for r in failed if r.get("error")]
        }


    def format_summary(self, metrics: Dict[str, Any]) -> str:
        """Format metrics into a human-readable summary."""
        lines = []
        lines.append(_c(f"=== {metrics['extraction_name'].title()} Metrics ===", "bold"))

        # Basic stats
        lines.append(f"Total items: {metrics['total_items']}")
        lines.append(f"Successful: {metrics['successful_items']} ({metrics['success_rate']:.1%})")
        if metrics['failed_items'] > 0:
            lines.append(_c(f"Failed: {metrics['failed_items']}", "yellow"))

        # Timing
        if metrics['total_time'] > 0:
            lines.append(f"Total time: {metrics['total_time']:.2f}s")
            if metrics['avg_processing_time'] > 0:
                lines.append(f"Avg processing time: {metrics['avg_processing_time']:.2f}s per item")

        # Token usage
        if metrics['total_tokens'] > 0:
            lines.append(_c("Token Usage:", "blue"))
            lines.append(f"  Input: {metrics['total_input_tokens']:,} (avg: {metrics['avg_input_tokens']:.1f})")
            lines.append(f"  Output: {metrics['total_output_tokens']:,} (avg: {metrics['avg_output_tokens']:.1f})")
            lines.append(f"  Total: {metrics['total_tokens']:,}")

        # Cost
        if metrics['total_cost'] > 0:
            lines.append(_c(f"Estimated cost: ${metrics['total_cost']:.6f}", "green"))

        return "\n".join(lines)


# Utility functions for creating metrics collectors
def create_metrics_collector(
    collector_type: str = "standard",
    name: str = "extraction",
    cost_per_input_token: float = 0.0,
    cost_per_output_token: float = 0.0,
    **kwargs
) -> BaseMetricsCollector:
    """Factory function to create metrics collectors."""
    if collector_type == "standard":
        return StandardMetricsCollector(
            name=name,
            cost_per_input_token=cost_per_input_token,
            cost_per_output_token=cost_per_output_token
        )
    else:
        raise ValueError(f"Unknown collector type: {collector_type}")


def save_metrics_json(metrics: Dict[str, Any], output_path: str) -> None:
    """Save metrics to JSON file."""
    from extraction.io import write_json
    write_json(output_path, metrics)


def load_metrics_json(input_path: str) -> Optional[Dict[str, Any]]:
    """Load metrics from JSON file."""
    from extraction.io import read_json
    return read_json(input_path)
