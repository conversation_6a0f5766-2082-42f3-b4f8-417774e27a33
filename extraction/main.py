#!/usr/bin/env python3
"""
Generic CLI for structured data extraction using LLMs.

Supports custom prompt builders, schemas, and data sources for flexible extraction workflows.
"""
from __future__ import annotations

import argparse
import importlib
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from extraction.io import read_json


def configure_logging(verbosity: int) -> None:
    """Configure logging with appropriate verbosity level."""
    level = logging.WARNING
    if verbosity == 1:
        level = logging.INFO
    elif verbosity >= 2:
        level = logging.DEBUG

    root = logging.getLogger()
    root.setLevel(level)

    # Remove existing handlers to avoid duplicate log lines
    for h in list(root.handlers):
        root.removeHandler(h)

    formatter = logging.Formatter("%(levelname)s: %(message)s")

    if verbosity > 0:
        try:
            from extraction.progress import TqdmLoggingHandler
            handler = TqdmLoggingHandler()
        except Exception:
            handler = logging.StreamHandler()
    else:
        handler = logging.StreamHandler()

    handler.setLevel(level)
    handler.setFormatter(formatter)
    root.addHandler(handler)


def load_data_from_file(file_path: str) -> List[Dict[str, Any]]:
    """Load data from JSON, JSONL, or text file."""
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"Input file not found: {file_path}")

    if path.suffix.lower() == '.jsonl':
        items = []
        with open(path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    items.append(json.loads(line))
        return items
    elif path.suffix.lower() == '.json':
        data = read_json(file_path)
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            return [data]
        else:
            raise ValueError(f"JSON file must contain a list or dict, got {type(data)}")
    elif path.suffix.lower() == '.txt':
        with open(path, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f if line.strip()]
        return [{"text": line} for line in lines]
    else:
        raise ValueError(f"Unsupported file format: {path.suffix}. Use .json, .jsonl, or .txt")


def load_prompt_builder_class(module_path: str, class_name: str):
    """Dynamically load a prompt builder class from a module."""
    try:
        module = importlib.import_module(module_path)
        return getattr(module, class_name)
    except ImportError as e:
        raise ImportError(f"Could not import module '{module_path}': {e}")
    except AttributeError as e:
        raise AttributeError(f"Class '{class_name}' not found in module '{module_path}': {e}")


def save_results(results: List[Dict[str, Any]], output_path: str, format: str = "json", include_prompts: bool = False) -> None:
    """Save extraction results to file, optionally including prompts for evaluation."""
    output_path_obj = Path(output_path)
    if output_path_obj.parent != Path('.'):  # Only create parent dirs if not current directory
        output_path_obj.parent.mkdir(parents=True, exist_ok=True)

    if format.lower() == "jsonl":
        with open(output_path_obj, 'w', encoding='utf-8') as f:
            for result in results:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')
    else:  # json
        with open(output_path_obj, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)


def save_evaluation_data(inputs: List[Dict[str, Any]], results: List[Dict[str, Any]], output_path: str) -> None:
    """Save inputs and results together for evaluation purposes."""
    evaluation_data = []

    for i, (input_data, result) in enumerate(zip(inputs, results)):
        eval_record = {
            "id": i,
            "input": {
                "messages": input_data.get("messages", []),
                "response_format": str(input_data.get("response_format", "")),
                "result_key": input_data.get("result_key", "")
            },
            "output": result.get(input_data.get("result_key", "result")) if not result.get("error") else None,
            "raw_result": result,
            "success": not bool(result.get("error")),
            "error": result.get("error")
        }
        evaluation_data.append(eval_record)

    output_path_obj = Path(output_path)
    if output_path_obj.parent != Path('.'):
        output_path_obj.parent.mkdir(parents=True, exist_ok=True)

    with open(output_path_obj, 'w', encoding='utf-8') as f:
        json.dump(evaluation_data, f, ensure_ascii=False, indent=2)





def cmd_extract(args: argparse.Namespace) -> int:
    """Generic extraction command that works with any prompt builder and data source."""

    # Load input data
    try:
        items = load_data_from_file(args.input)
        logging.info(f"Loaded {len(items)} items from {args.input}")
    except Exception as e:
        print(f"Error loading input data: {e}", file=sys.stderr)
        return 1

    # Load prompt builder class
    try:
        prompt_builder_class = load_prompt_builder_class(args.prompt_module, args.prompt_class)

        # Initialize prompt builder with any additional arguments
        prompt_kwargs = {}
        if hasattr(args, 'prompt_args') and args.prompt_args:
            # Parse prompt_args as key=value pairs
            for arg in args.prompt_args:
                if '=' in arg:
                    key, value = arg.split('=', 1)
                    prompt_kwargs[key] = value
                else:
                    print(f"Warning: Ignoring invalid prompt argument format: {arg}", file=sys.stderr)

        prompt_builder = prompt_builder_class(**prompt_kwargs)
        logging.info(f"Initialized prompt builder: {args.prompt_class}")
    except Exception as e:
        print(f"Error loading prompt builder: {e}", file=sys.stderr)
        return 2


    # Build inputs for LLM
    try:
        inputs = []
        for item in items:
            built = prompt_builder.build(item)
            if not built.get("messages"):
                logging.warning(f"Skipping item with no messages: {item}")
                continue
            inputs.append(built)

        logging.info(f"Built {len(inputs)} valid inputs for processing")
        if not inputs:
            print("No valid inputs to process", file=sys.stderr)
            return 3
    except Exception as e:
        print(f"Error building inputs: {e}", file=sys.stderr)
        return 4

    # Run extraction using LLM with metrics
    try:
        from extraction.inference import get_language_model
        from extraction.metrics import create_metrics_collector

        # Create metrics collector
        cost_per_input = getattr(args, 'cost_per_input_token', 0.0)
        cost_per_output = getattr(args, 'cost_per_output_token', 0.0)
        metrics_collector = create_metrics_collector(
            collector_type="standard",
            name=f"{args.prompt_class}_extraction",
            cost_per_input_token=cost_per_input,
            cost_per_output_token=cost_per_output
        )

        lm = get_language_model(
            provider=args.provider,
            api_key=args.api_key,
            base_url=args.base_url,
            model=args.model
        )

        logging.info(f"Starting extraction with {args.provider} provider")
        metrics_collector.start_timing()

        results, effective_workers = lm.generate(inputs, max_workers=args.max_workers)

        metrics_collector.end_timing()
        logging.info(f"Extraction completed in {metrics_collector.get_total_time():.2f}s using {effective_workers} workers")

    except Exception as e:
        print(f"Error during extraction: {e}", file=sys.stderr)
        return 5

    # Save results and metrics
    try:
        save_results(results, args.output, args.format)
        logging.info(f"Results saved to {args.output}")

        # Save evaluation data if requested
        if hasattr(args, 'save_evaluation') and args.save_evaluation:
            eval_path = args.output.replace('.json', '_evaluation.json').replace('.jsonl', '_evaluation.json')
            save_evaluation_data(inputs, results, eval_path)
            logging.info(f"Evaluation data saved to {eval_path}")

        # Collect and display metrics
        metrics = metrics_collector.collect_metrics(results)
        print(metrics_collector.format_summary(metrics))

        # Save metrics if requested
        if hasattr(args, 'save_metrics') and args.save_metrics:
            metrics_path = args.output.replace('.json', '_metrics.json').replace('.jsonl', '_metrics.json')
            from extraction.metrics import save_metrics_json
            save_metrics_json(metrics, metrics_path)
            logging.info(f"Metrics saved to {metrics_path}")

    except Exception as e:
        print(f"Error saving results: {e}", file=sys.stderr)
        return 6

    return 0







def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="Generic structured data extraction using LLMs.")

    # Add subcommands
    subparsers = p.add_subparsers(dest='command', help='Available commands', required=True)

    # Extract command (main generic extraction)
    extract_parser = subparsers.add_parser('extract', help='Extract structured data from input files')
    extract_parser.add_argument("input", help="Input file (.json, .jsonl, or .txt)")
    extract_parser.add_argument("prompt_module", help="Python module containing prompt builder (e.g., 'my_package.my_prompt')")
    extract_parser.add_argument("prompt_class", help="Prompt builder class name (e.g., 'MyPromptBuilder')")

    extract_parser.add_argument("-o", "--output", default="output.json", help="Output file path (default: output.json)")
    extract_parser.add_argument("--format", choices=["json", "jsonl"], default="json", help="Output format (default: json)")
    extract_parser.add_argument("--verbose", "-v", action="count", default=0, help="Increase logging verbosity (-v, -vv)")

    # Prompt builder arguments
    extract_parser.add_argument("--prompt-args", nargs="*", help="Arguments for prompt builder as key=value pairs")

    # LLM options
    extract_parser.add_argument("--provider", default="openai", help="LLM provider (openai, ollama, or custom)")
    extract_parser.add_argument("--api-key", help="API key (if required by provider)")
    extract_parser.add_argument("--base-url", help="Override base URL (optional)")
    extract_parser.add_argument("--model", help="Model name (provider-specific)")
    extract_parser.add_argument("--max-workers", type=int, help="Max worker threads for batch processing")

    # Metrics options
    extract_parser.add_argument("--save-metrics", action="store_true", help="Save metrics to JSON file")
    extract_parser.add_argument("--cost-per-input-token", type=float, default=0.0, help="Cost per input token for cost calculation")
    extract_parser.add_argument("--cost-per-output-token", type=float, default=0.0, help="Cost per output token for cost calculation")

    # Evaluation options
    extract_parser.add_argument("--save-evaluation", action="store_true", help="Save evaluation data (inputs + outputs) for assessment")



    return p


def main(argv: List[str]) -> int:
    parser = build_parser()
    args = parser.parse_args(argv)
    configure_logging(getattr(args, 'verbose', 0))

    # Handle commands
    if args.command == 'extract':
        return cmd_extract(args)
    else:
        print(f"Unknown command: {args.command}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
