from __future__ import annotations

import os

from extraction.inference import get_language_model
from .my_prompt import ProductPrompt, EvaluationPrompt
from .my_language_model import CustomLanguageModel  # Import triggers registration as "custom" provider


def format_evaluation_record(record):
    input_prompt = record[0]["messages"][0]["content"]
    expected_format = record[0]["response_format"].model_json_schema()
    extraction_output = record[1]["product"]

    return {
        "input_prompt": input_prompt,
        "expected_format": expected_format,
        "extraction_output": extraction_output
    }


api_key = os.getenv("OPENAI_API_KEY")

items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
    {"text": "Device Z sells for $129.00 with premium materials and AI processing"},
]

inputs = [ProductPrompt().build(x) for x in items]

# Choose your language model provider
# Option 1: Use custom mock model
lm = get_language_model(provider="custom")
print("Using custom language model")

# Option 2: Use OpenAI (uncomment to use)
# lm = get_language_model(provider="openai")
# print("Using OpenAI")

# Option 3: Use Ollama (uncomment to use)
# lm = get_language_model(provider="ollama")
# print("Using Ollama")

results, _ = lm.generate(inputs)

for i, r in enumerate(results):
    print(i, r.get("product"))

formatted_records = [EvaluationPrompt().build(format_evaluation_record(r))for r in zip(inputs, results)]

evaluation_results, _ = lm.generate(formatted_records)

for i, r in enumerate(evaluation_results):
    print(i, r.get("evaluation"))