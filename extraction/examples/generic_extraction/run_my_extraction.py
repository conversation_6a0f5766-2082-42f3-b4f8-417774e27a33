"""
Example of using the extraction library for structured data extraction.

This demonstrates how to use the extraction package as a library without CLI functionality.
Run this as a Python module or import the functions for use in your own code.
"""
from __future__ import annotations

import os
from typing import Dict, Any

from extraction.inference import get_language_model
from .my_prompt import ProductPrompt, EvaluationPrompt
from .my_language_model import CustomLanguageModel  # Import triggers registration as "custom" provider

def format_evaluation_record(record):
    """Format a record for evaluation by combining input and output."""
    input_prompt = record[0]["messages"][0]["content"]
    expected_format = record[0]["response_format"].model_json_schema()
    extraction_output = record[1]["product"]

    return {
        "input_prompt": input_prompt,
        "expected_format": expected_format,
        "extraction_output": extraction_output
    }


items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
    {"text": "Device Z sells for $129.00 with premium materials and AI processing"},
]

# Build inputs using the prompt builder
    inputs = [ProductPrompt().build(x) for x in items]

    # Get language model
    lm = get_language_model(provider=provider)
    print(f"Using {provider} language model")

    # Run extraction
    results, effective_workers = lm.generate(inputs)
    print(f"Extraction completed using {effective_workers} workers")

    # Display extraction results
    print("\nExtraction Results:")
    for i, r in enumerate(results):
        print(f"{i}: {r.get('product')}")






def run_extraction_example(provider: str = "custom") -> Dict[str, Any]:
    """
    Run a complete extraction example with the specified provider.

    Args:
        provider: Language model provider to use ("custom", "openai", "ollama")

    Returns:
        Dictionary containing extraction results and evaluation results
    """
    # Sample data for extraction
    items = [
        {"text": "Widget X costs $19.99 and includes wifi, gps."},
        {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
        {"text": "Device Z sells for $129.00 with premium materials and AI processing"},
    ]

    # Build inputs using the prompt builder
    inputs = [ProductPrompt().build(x) for x in items]

    # Get language model
    lm = get_language_model(provider=provider)
    print(f"Using {provider} language model")

    # Run extraction
    results, effective_workers = lm.generate(inputs)
    print(f"Extraction completed using {effective_workers} workers")

    # Display extraction results
    print("\nExtraction Results:")
    for i, r in enumerate(results):
        print(f"{i}: {r.get('product')}")

    # Format records for evaluation
    formatted_records = [EvaluationPrompt().build(format_evaluation_record(r)) for r in zip(inputs, results)]

    # Run evaluation
    evaluation_results, _ = lm.generate(formatted_records)

    # Display evaluation results
    print("\nEvaluation Results:")
    for i, r in enumerate(evaluation_results):
        print(f"{i}: {r.get('evaluation')}")

    return {
        "extraction_results": results,
        "evaluation_results": evaluation_results,
        "items_processed": len(items),
        "workers_used": effective_workers
    }


def main():
    """Main function for running the example."""
    # Check for API key if using OpenAI
    api_key = os.getenv("OPENAI_API_KEY")

    # Choose provider based on available credentials
    if api_key:
        print("OpenAI API key found - you can use provider='openai'")

    # Run with custom provider by default (no API key required)
    results = run_extraction_example(provider="custom")

    print(f"\nSummary: Processed {results['items_processed']} items using {results['workers_used']} workers")
    return results


if __name__ == "__main__":
    main()