"""
Unit tests for extraction/metrics.py module.

Tests metrics collection and reporting functionality.
"""
import unittest
import tempfile
import os
import json
import time
from unittest.mock import patch, MagicMock


class TestBaseMetricsCollector(unittest.TestCase):
    """Test cases for BaseMetricsCollector abstract class."""

    def test_base_metrics_collector_is_abstract(self):
        """Test that BaseMetricsCollector cannot be instantiated directly."""
        from extraction.metrics import BaseMetricsCollector
        
        with self.assertRaises(TypeError):
            BaseMetricsCollector()

    def test_abstract_methods_exist(self):
        """Test that abstract methods are defined."""
        from extraction.metrics import BaseMetricsCollector
        
        # Create a concrete subclass without implementing abstract methods
        class IncompleteCollector(BaseMetricsCollector):
            pass
        
        with self.assertRaises(TypeError):
            IncompleteCollector()


class TestStandardMetricsCollector(unittest.TestCase):
    """Test cases for StandardMetricsCollector."""

    def setUp(self):
        """Set up test fixtures."""
        from extraction.metrics import StandardMetricsCollector
        self.collector = StandardMetricsCollector(
            name="test_extraction",
            cost_per_input_token=0.001,
            cost_per_output_token=0.002
        )

    def test_initialization(self):
        """Test StandardMetricsCollector initialization."""
        self.assertEqual(self.collector.name, "test_extraction")
        self.assertEqual(self.collector.cost_per_input_token, 0.001)
        self.assertEqual(self.collector.cost_per_output_token, 0.002)
        self.assertIsNone(self.collector.start_time)
        self.assertIsNone(self.collector.end_time)

    def test_timing_methods(self):
        """Test start_timing and end_timing methods."""
        start_time = time.time()
        self.collector.start_timing()
        
        self.assertIsNotNone(self.collector.start_time)
        self.assertGreaterEqual(self.collector.start_time, start_time)
        
        time.sleep(0.01)  # Small delay
        
        self.collector.end_timing()
        self.assertIsNotNone(self.collector.end_time)
        self.assertGreater(self.collector.end_time, self.collector.start_time)

    def test_get_total_time(self):
        """Test get_total_time method."""
        # Before timing
        self.assertEqual(self.collector.get_total_time(), 0.0)
        
        # After timing
        self.collector.start_timing()
        time.sleep(0.01)
        self.collector.end_timing()
        
        total_time = self.collector.get_total_time()
        self.assertGreater(total_time, 0.0)
        self.assertLess(total_time, 1.0)  # Should be very small

    def test_collect_metrics_empty_results(self):
        """Test collect_metrics with empty results."""
        metrics = self.collector.collect_metrics([])
        
        expected = {
            "total_items": 0,
            "successful_items": 0,
            "failed_items": 0
        }
        self.assertEqual(metrics, expected)

    def test_collect_metrics_with_results(self):
        """Test collect_metrics with sample results."""
        results = [
            {"input_tokens": 100, "output_tokens": 50, "result": "success"},
            {"input_tokens": 150, "output_tokens": 75, "result": "success"},
            {"input_tokens": 80, "output_tokens": 0, "error": "failed", "result": None}
        ]
        
        metrics = self.collector.collect_metrics(results)
        
        self.assertEqual(metrics["total_items"], 3)
        self.assertEqual(metrics["successful_items"], 2)
        self.assertEqual(metrics["failed_items"], 1)
        self.assertEqual(metrics["total_input_tokens"], 330)
        self.assertEqual(metrics["total_output_tokens"], 125)
        self.assertEqual(metrics["avg_input_tokens"], 110.0)
        self.assertEqual(metrics["avg_output_tokens"], 41.666666666666664)
        
        # Cost calculation: (330 * 0.001) + (125 * 0.002) = 0.33 + 0.25 = 0.58
        self.assertAlmostEqual(metrics["total_cost"], 0.58, places=3)

    def test_format_summary(self):
        """Test format_summary method."""
        metrics = {
            "total_items": 10,
            "successful_items": 8,
            "failed_items": 2,
            "total_input_tokens": 1000,
            "total_output_tokens": 500,
            "avg_input_tokens": 100.0,
            "avg_output_tokens": 50.0,
            "total_cost": 1.5,
            "success_rate": 0.8
        }
        
        self.collector.start_timing()
        time.sleep(0.01)
        self.collector.end_timing()
        
        summary = self.collector.format_summary(metrics)
        
        self.assertIn("test_extraction", summary)
        self.assertIn("10 items", summary)
        self.assertIn("8 successful", summary)
        self.assertIn("2 failed", summary)
        self.assertIn("1000 input tokens", summary)
        self.assertIn("500 output tokens", summary)
        self.assertIn("$1.50", summary)


class TestCreateMetricsCollector(unittest.TestCase):
    """Test cases for create_metrics_collector factory function."""

    def test_create_standard_collector(self):
        """Test creating standard metrics collector."""
        from extraction.metrics import create_metrics_collector, StandardMetricsCollector
        
        collector = create_metrics_collector(
            collector_type="standard",
            name="test",
            cost_per_input_token=0.001,
            cost_per_output_token=0.002
        )
        
        self.assertIsInstance(collector, StandardMetricsCollector)
        self.assertEqual(collector.name, "test")
        self.assertEqual(collector.cost_per_input_token, 0.001)
        self.assertEqual(collector.cost_per_output_token, 0.002)

    def test_create_unknown_collector_raises_error(self):
        """Test creating unknown collector type raises ValueError."""
        from extraction.metrics import create_metrics_collector
        
        with self.assertRaises(ValueError) as context:
            create_metrics_collector(collector_type="unknown")
        
        self.assertIn("Unknown collector", str(context.exception))

    def test_create_collector_default_values(self):
        """Test creating collector with default values."""
        from extraction.metrics import create_metrics_collector, StandardMetricsCollector
        
        collector = create_metrics_collector()
        
        self.assertIsInstance(collector, StandardMetricsCollector)
        self.assertEqual(collector.name, "extraction")
        self.assertEqual(collector.cost_per_input_token, 0.0)
        self.assertEqual(collector.cost_per_output_token, 0.0)


class TestSaveMetricsJson(unittest.TestCase):
    """Test cases for save_metrics_json function."""

    def test_save_metrics_json(self):
        """Test saving metrics to JSON file."""
        from extraction.metrics import save_metrics_json
        from extraction.io import read_json
        
        test_metrics = {
            "total_items": 5,
            "successful_items": 4,
            "failed_items": 1,
            "total_cost": 2.5
        }
        
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            save_metrics_json(test_metrics, temp_path)
            
            # Verify file was written correctly
            result = read_json(temp_path)
            self.assertEqual(result, test_metrics)
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_save_metrics_json_creates_directories(self):
        """Test that save_metrics_json creates parent directories."""
        from extraction.metrics import save_metrics_json
        from extraction.io import read_json
        
        test_metrics = {"test": "data"}
        
        with tempfile.TemporaryDirectory() as temp_dir:
            nested_path = os.path.join(temp_dir, "subdir", "metrics.json")
            
            save_metrics_json(test_metrics, nested_path)
            
            # Verify file was written and directories created
            self.assertTrue(os.path.exists(nested_path))
            result = read_json(nested_path)
            self.assertEqual(result, test_metrics)


if __name__ == '__main__':
    unittest.main()
