from __future__ import annotations

from typing import Any, Dict, List, <PERSON>ple
import logging

from .prompt import ArxivAffiliationPromptBuilder


def select_papers_missing_affiliations(papers: List[dict]) -> Tuple[List[dict], List[int]]:
    """Return papers that still need affiliations and their original indices.

    Filters for papers without the "affiliations" field present.
    """
    to_process: List[dict] = []
    indices: List[int] = []
    for i, paper in enumerate(papers):
        if not paper.get("affiliations"):
            to_process.append(paper)
            indices.append(i)
    return to_process, indices


def build_inputs_for_papers(papers_to_process: List[dict], *, orgs_text: str) -> List[Dict[str, Any]]:
    """Build model-ready inputs for the affiliations extraction task.

    Uses ArxivAffiliationPromptBuilder to format prompts with the notable orgs list.
    """
    prompter = ArxivAffiliationPromptBuilder(orgs_text=orgs_text)
    inputs: List[Dict[str, Any]] = []
    for p in papers_to_process:
        built = prompter.build(p)
        if not built.get("messages"):
            logging.warning("Skipping paper with no messages constructed: %s", p.get("title", "(untitled)"))
            inputs.append({"messages": [], "result_key": built.get("result_key", "result")})
            continue
        inputs.append(built)
    return inputs


def apply_results_to_papers(
    papers: List[dict],
    paper_indices: List[int],
    batch_results: List[Dict[str, Any]],
    *,
    batch_start_time: float | None = None,
    batch_end_time: float | None = None,
    effective_max_workers: int | None = None,
) -> bool:
    """Apply batch results back to the original papers list.

    Returns True if any updates were applied.
    """
    updated = False
    for result, paper_idx in zip(batch_results, paper_indices):
        paper = papers[paper_idx]
        if result.get("error"):
            logging.error("Failed to get affiliations for %s: %s", paper.get("title", ""), result["error"])
            continue
        paper["affiliations"] = result.get("affiliations")
        paper["input_tokens"] = result.get("input_tokens", 0)
        paper["output_tokens"] = result.get("output_tokens", 0)
        if batch_start_time is not None and batch_end_time is not None:
            paper["batch_processing_time"] = round(batch_end_time - batch_start_time, 2)
        if paper_indices:
            paper["batch_size"] = len(paper_indices)
        if effective_max_workers is not None:
            paper["max_workers_used"] = effective_max_workers
        updated = True
    return updated
