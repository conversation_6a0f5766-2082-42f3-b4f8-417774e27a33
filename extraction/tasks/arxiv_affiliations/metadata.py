from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import List
import logging
import ssl
import time
import urllib.parse
import urllib.request
import xml.etree.ElementTree as ET

try:
    import certifi
except ImportError:
    certifi = None

ARXIV_API_BASE = "http://export.arxiv.org/api/query"
ATOM_NS = {
    "atom": "http://www.w3.org/2005/Atom",
    "opensearch": "http://a9.com/-/spec/opensearch/1.1/",
    "arxiv": "http://arxiv.org/schemas/atom",
}

REQUEST_DELAY_SECONDS = 3


@dataclass
class ArxivQueryParams:
    category: str
    start_utc: datetime
    end_utc: datetime
    start_index: int = 0
    max_results: int = 100

    def to_query_string(self) -> str:
        date_range = f"[{self.start_utc.strftime('%Y%m%d%H%M')} TO {self.end_utc.strftime('%Y%m%d%H%M')}]"
        search_query = f"cat:{self.category} AND submittedDate:{date_range}"
        params = {
            "search_query": search_query,
            "start": str(self.start_index),
            "max_results": str(self.max_results),
            "sortBy": "submittedDate",
            "sortOrder": "ascending",
        }
        return urllib.parse.urlencode(params)


def _build_query_url(params: ArxivQueryParams) -> str:
    return f"{ARXIV_API_BASE}?{params.to_query_string()}"


def _fetch_url(url: str, timeout: int = 20) -> bytes:
    logging.debug("Fetching URL: %s", url)
    req = urllib.request.Request(url, headers={"User-Agent": "paper-scraper/0.1 (https://arxiv.org)"})
    
    # Create SSL context with proper certificate verification
    ssl_context = ssl.create_default_context()
    if certifi:
        ssl_context = ssl.create_default_context(cafile=certifi.where())
    
    with urllib.request.urlopen(req, timeout=timeout, context=ssl_context) as resp:
        if resp.status != 200:
            raise ConnectionError(f"arXiv API returned status {resp.status}")
        return resp.read()


def _parse_atom_for_metadata(atom_xml: bytes) -> List[dict]:
    try:
        root = ET.fromstring(atom_xml)
    except ET.ParseError as e:
        raise ValueError(f"Failed to parse arXiv Atom feed: {e}")

    items: List[dict] = []
    for entry in root.findall("atom:entry", ATOM_NS):
        entry_id = None
        abs_url = None
        pdf_url = None
        title = None
        summary = None
        published = None
        updated = None
        authors: List[str] = []
        primary_category = None
        categories: List[str] = []

        id_elem = entry.find("atom:id", ATOM_NS)
        if id_elem is not None and id_elem.text:
            entry_id = id_elem.text.strip().replace("http://arxiv.org/abs/", "").replace("https://arxiv.org/abs/", "")
            abs_url = id_elem.text.strip()

        alt = entry.find("atom:link[@rel='alternate']", ATOM_NS)
        if alt is not None and alt.get("href"):
            abs_url = alt.get("href")
        pdf = entry.find("atom:link[@title='pdf']", ATOM_NS)
        if pdf is not None and pdf.get("href"):
            pdf_url = pdf.get("href")

        t = entry.find("atom:title", ATOM_NS)
        if t is not None and t.text:
            title = t.text.strip()
        s = entry.find("atom:summary", ATOM_NS)
        if s is not None and s.text:
            summary = s.text.strip()
        p = entry.find("atom:published", ATOM_NS)
        if p is not None and p.text:
            published = p.text.strip()
        u = entry.find("atom:updated", ATOM_NS)
        if u is not None and u.text:
            updated = u.text.strip()

        for a in entry.findall("atom:author", ATOM_NS):
            n = a.find("atom:name", ATOM_NS)
            if n is not None and n.text:
                authors.append(n.text.strip())

        pc = entry.find("arxiv:primary_category", ATOM_NS)
        if pc is not None:
            term = pc.get("term")
            if term:
                primary_category = term
        for c in entry.findall("atom:category", ATOM_NS):
            term = c.get("term")
            if term:
                categories.append(term)

        if abs_url:
            abs_url = abs_url.replace("http://", "https://")
            if abs_url.rsplit("/", 1)[-1].count("v") == 1:
                try:
                    base, last = abs_url.rsplit("/", 1)
                    paper_id = last.split("v")[0]
                    abs_url = f"{base}/{paper_id}"
                except Exception:
                    pass

        items.append({
            "id": entry_id,
            "abs_url": abs_url,
            "pdf_url": pdf_url,
            "title": title,
            "summary": summary,
            "published": published,
            "updated": updated,
            "authors": authors,
            "primary_category": primary_category,
            "categories": categories,
        })

    return items


def fetch_metadata_for_category(category: str, date_str: str, limit: int | None = None) -> List[dict]:
    """
    Fetch arXiv metadata for any category and date.

    Args:
        category: arXiv category (e.g., 'cs.AI', 'cs.CV', 'cs.CL', 'math.CO', 'physics.optics')
        date_str: Date in YYYY-MM-DD format
        limit: Maximum number of papers to return (None for all)

    Returns:
        List of paper metadata dictionaries
    """
    try:
        day = datetime.strptime(date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
    except ValueError:
        raise ValueError("Invalid date format. Expected YYYY-MM-DD.")

    start_utc = day.replace(hour=0, minute=0)
    end_utc = start_utc + timedelta(days=1)

    results: List[dict] = []
    start_index = 0
    page_size = 200

    while True:
        params = ArxivQueryParams(
            category=category,
            start_utc=start_utc,
            end_utc=end_utc,
            start_index=start_index,
            max_results=page_size,
        )
        url = _build_query_url(params)
        try:
            data = _fetch_url(url)
        except Exception as e:
            logging.error("Network error calling arXiv API: %s", e)
            raise

        page_items = _parse_atom_for_metadata(data)
        logging.info("Fetched %d results (start=%d)", len(page_items), start_index)

        if not page_items:
            break

        results.extend(page_items)

        if limit is not None and len(results) >= limit:
            break

        if len(page_items) < page_size:
            break

        start_index += page_size
        time.sleep(REQUEST_DELAY_SECONDS)

    seen = set()
    deduped: List[dict] = []
    for item in results:
        key = item.get("abs_url") or item.get("id")
        if not key:
            continue
        key = str(key)
        if key in seen:
            continue
        seen.add(key)
        if item.get("abs_url"):
            item["abs_url"] = item["abs_url"].replace("http://", "https://")
        if item.get("pdf_url"):
            item["pdf_url"] = item["pdf_url"].replace("http://", "https://")
        deduped.append(item)

    if limit is not None:
        deduped = deduped[:limit]

    return deduped


def fetch_cs_ai_metadata_for_date(date_str: str, limit: int | None = None) -> List[dict]:
    """
    Fetch cs.AI metadata for a date (backward compatibility wrapper).

    Args:
        date_str: Date in YYYY-MM-DD format
        limit: Maximum number of papers to return (None for all)

    Returns:
        List of paper metadata dictionaries
    """
    return fetch_metadata_for_category("cs.AI", date_str, limit)
