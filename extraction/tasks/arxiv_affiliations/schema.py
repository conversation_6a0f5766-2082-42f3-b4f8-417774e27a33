from __future__ import annotations

from typing import List
from pydantic import BaseModel, Field


class AuthorAffiliation(BaseModel):
    """Represents a single affiliation for an author that is notable."""

    author_name: str = Field(..., description="Full author name")
    organization: str = Field(..., description="Matched notable organization name")


class AllAuthorAffiliation(BaseModel):
    """Represents any affiliation extracted for an author, with a notable flag."""

    author_name: str
    organization: str
    is_notable: bool


class PaperAffiliationAnalysis(BaseModel):
    """Top-level schema returned by the LLM extraction step for arXiv affiliations."""

    paper_title: str
    all_affiliations: List[AllAuthorAffiliation]
    notable_affiliations: List[AuthorAffiliation]
    has_notable_affiliations: bool
