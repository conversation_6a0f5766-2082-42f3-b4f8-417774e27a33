from __future__ import annotations

from typing import Any, Dict

from .schema import PaperAffiliationAnalysis
from ...prompting import Base<PERSON>romptBuilder


# Backwards-compatible alias name for earlier code references
AffiliationPromptBuilder = None  # deprecated alias; not used in core

class ArxivAffiliationPromptBuilder(BasePromptBuilder):
    """Builds prompts/messages for extracting affiliations from arXiv paper data."""

    SYSTEM_INSTRUCTION = (
        "Extract ALL author affiliations from research papers and identify which match notable organizations."
    )

    def __init__(self, *, orgs_text: str):
        self.orgs_text = orgs_text

    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        paper_title: str = item.get("title", "")
        paper_text: str = item.get("first_page_text", "")
        orgs_text = self.orgs_text

        prompt = f"""
You are tasked with extracting ALL author affiliations from a research paper and determining which ones match the provided notable organizations.

Paper title: {paper_title}

First page text: {paper_text}

Notable organizations to check against:
{orgs_text}

Extract ALL author affiliations from the paper, then:
1. Mark each affiliation as notable (is_notable: true) if it matches any organization in the provided list
2. Include all affiliations in all_affiliations
3. Include only notable affiliations in notable_affiliations
4. Set has_notable_affiliations to true if any affiliations match the notable organizations list

Return comprehensive affiliation data for all authors.
"""
        return {
            "messages": [
                {"role": "system", "content": self.SYSTEM_INSTRUCTION},
                {"role": "user", "content": prompt},
            ],
            "response_format": PaperAffiliationAnalysis,
            "result_key": "affiliations",
        }
