"""
Schema for LLM-based evaluation of extraction results.
"""
from __future__ import annotations

from pydantic import BaseModel, Field
from typing import Optional


class EvalSpec(BaseModel):
    """Schema for LLM evaluation of extraction quality."""
    
    correct: bool = Field(
        description="Whether the extraction is factually correct based on the input text"
    )
    
    formatted: bool = Field(
        description="Whether the extraction follows the expected format/schema properly"
    )
    
    reasoning: Optional[str] = Field(
        default=None,
        description="Brief explanation of the evaluation decision"
    )
    
    confidence: Optional[float] = Field(
        default=None,
        description="Confidence score from 0.0 to 1.0 for this evaluation",
        ge=0.0,
        le=1.0
    )
