"""
Prompt builder for LLM-based evaluation of extraction results.
"""
from __future__ import annotations

from typing import Any, Dict, List
from extraction.prompting import BasePromptBuilder
from .schema import EvalSpec


class EvaluationPromptBuilder(BasePromptBuilder):
    """Builds prompts for LLM to evaluate extraction quality."""
    
    def __init__(self, task_description: str = "extraction", expected_schema: str = ""):
        """
        Initialize evaluation prompt builder.
        
        Args:
            task_description: Description of the original extraction task
            expected_schema: Description of the expected output schema
        """
        self.task_description = task_description
        self.expected_schema = expected_schema
    
    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build evaluation prompt from input text and extraction output.
        
        Expected item format:
        {
            "input_text": "original text that was extracted from",
            "extraction_output": "the LLM's extraction result",
            "expected_format": "description of expected format (optional)"
        }
        """
        input_text = item.get("input_text", "")
        extraction_output = item.get("extraction_output", "")
        expected_format = item.get("expected_format", self.expected_schema)
        
        # Handle case where extraction failed (None/null output)
        if extraction_output is None:
            extraction_output = "NULL (extraction failed)"
        elif isinstance(extraction_output, dict):
            import json
            extraction_output = json.dumps(extraction_output, indent=2)
        else:
            extraction_output = str(extraction_output)
        
        prompt = self._build_evaluation_prompt(
            input_text=input_text,
            extraction_output=extraction_output,
            expected_format=expected_format,
            task_description=self.task_description
        )
        
        return {
            "messages": [{"role": "user", "content": prompt}],
            "response_format": EvalSpec,
            "result_key": "evaluation"
        }
    
    def _build_evaluation_prompt(
        self, 
        input_text: str, 
        extraction_output: str, 
        expected_format: str,
        task_description: str
    ) -> str:
        """Build the evaluation prompt."""
        
        prompt = f"""You are an expert evaluator assessing the quality of {task_description} results.

**Original Input Text:**
{input_text}

**Extraction Output:**
{extraction_output}

**Expected Format:**
{expected_format}

**Your Task:**
Evaluate the extraction result on two key criteria:

1. **CORRECT**: Is the extracted information factually accurate based on the input text?
   - Check if the extracted data matches what's actually stated in the input
   - Verify numbers, names, and other factual details are correct
   - Consider if important information was missed or hallucinated

2. **FORMATTED**: Does the output follow the expected format/schema properly?
   - Check if required fields are present
   - Verify data types are correct (strings, numbers, lists, etc.)
   - Assess if the structure matches expectations
   - Note: If extraction failed (NULL), this should be false

**Additional Guidelines:**
- If the extraction output is "NULL (extraction failed)", both correct and formatted should typically be false
- Be strict but fair in your evaluation
- Provide brief reasoning for your decision
- Give a confidence score (0.0-1.0) for how certain you are about this evaluation

Respond with your evaluation in the specified JSON format."""

        return prompt


class ProductEvaluationPromptBuilder(EvaluationPromptBuilder):
    """Specialized evaluation prompt builder for product extraction tasks."""
    
    def __init__(self):
        super().__init__(
            task_description="product information extraction",
            expected_schema="JSON object with fields: name (string), price (number), features (array of strings)"
        )


class GenericEvaluationPromptBuilder(EvaluationPromptBuilder):
    """Generic evaluation prompt builder that can be configured for any task."""
    
    def __init__(self, task_name: str, schema_description: str):
        super().__init__(
            task_description=task_name,
            expected_schema=schema_description
        )
