"""
LLM-based evaluation task for assessing extraction quality.

This module provides tools to evaluate extraction results using another LLM,
following the same pattern as other extraction tasks.
"""

from .schema import EvalSpec
from .prompt import EvaluationPromptBuilder, ProductEvaluationPromptBuilder, GenericEvaluationPromptBuilder
from .prepare import (
    prepare_evaluation_items_from_file,
    prepare_evaluation_items_from_data,
    run_evaluation_extraction,
    compute_evaluation_summary
)

__all__ = [
    "EvalSpec",
    "EvaluationPromptBuilder",
    "ProductEvaluationPromptBuilder", 
    "GenericEvaluationPromptBuilder",
    "prepare_evaluation_items_from_file",
    "prepare_evaluation_items_from_data",
    "run_evaluation_extraction",
    "compute_evaluation_summary"
]
