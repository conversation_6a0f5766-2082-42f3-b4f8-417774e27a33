"""
Utilities for preparing evaluation data from extraction results.
"""
from __future__ import annotations

import json
from typing import Any, Dict, List, Optional


def prepare_evaluation_items_from_file(evaluation_data_path: str) -> List[Dict[str, Any]]:
    """
    Load evaluation data file and prepare items for LLM evaluation.
    
    Args:
        evaluation_data_path: Path to evaluation JSON file created by --save-evaluation
        
    Returns:
        List of items ready for evaluation prompt builder
    """
    from extraction.io import read_json
    
    evaluation_data = read_json(evaluation_data_path, default=[])
    if not evaluation_data:
        raise ValueError(f"No evaluation data found in {evaluation_data_path}")
    
    items = []
    for record in evaluation_data:
        # Extract the original input text from messages
        input_messages = record.get("input", {}).get("messages", [])
        input_text = ""
        for msg in input_messages:
            if msg.get("role") == "user":
                input_text = msg.get("content", "")
                break
        
        # Get the extraction output
        extraction_output = record.get("output")
        
        # Get expected format info
        response_format = record.get("input", {}).get("response_format", "")
        
        item = {
            "id": record.get("id"),
            "input_text": input_text,
            "extraction_output": extraction_output,
            "expected_format": response_format,
            "original_success": record.get("success", False),
            "original_error": record.get("error")
        }
        items.append(item)
    
    return items


def prepare_evaluation_items_from_data(
    input_texts: List[str], 
    extraction_outputs: List[Any],
    expected_format: str = "",
    ids: Optional[List[Any]] = None
) -> List[Dict[str, Any]]:
    """
    Prepare evaluation items from raw input texts and extraction outputs.
    
    Args:
        input_texts: List of original input texts
        extraction_outputs: List of extraction results to evaluate
        expected_format: Description of expected output format
        ids: Optional list of IDs for each item
        
    Returns:
        List of items ready for evaluation prompt builder
    """
    if len(input_texts) != len(extraction_outputs):
        raise ValueError("input_texts and extraction_outputs must have same length")
    
    items = []
    for i, (input_text, output) in enumerate(zip(input_texts, extraction_outputs)):
        item = {
            "id": ids[i] if ids else i,
            "input_text": input_text,
            "extraction_output": output,
            "expected_format": expected_format
        }
        items.append(item)
    
    return items


def run_evaluation_extraction(
    items: List[Dict[str, Any]],
    prompt_builder,
    language_model,
    max_workers: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    Run LLM-based evaluation on prepared items.
    
    Args:
        items: Prepared evaluation items
        prompt_builder: Evaluation prompt builder instance
        language_model: Language model instance
        max_workers: Max workers for parallel processing
        
    Returns:
        List of evaluation results
    """
    # Build prompts
    inputs = []
    for item in items:
        built = prompt_builder.build(item)
        inputs.append(built)
    
    # Run LLM evaluation
    results, _ = language_model.generate(inputs, max_workers=max_workers)
    
    # Combine with original item data
    evaluation_results = []
    for i, (item, result) in enumerate(zip(items, results)):
        eval_result = {
            "id": item.get("id", i),
            "input_text": item["input_text"],
            "extraction_output": item["extraction_output"],
            "evaluation": result.get("evaluation"),
            "evaluation_success": not bool(result.get("error")),
            "evaluation_error": result.get("error"),
            "input_tokens": result.get("input_tokens", 0),
            "output_tokens": result.get("output_tokens", 0)
        }
        
        # Add original metadata if available
        if "original_success" in item:
            eval_result["original_success"] = item["original_success"]
        if "original_error" in item:
            eval_result["original_error"] = item["original_error"]
            
        evaluation_results.append(eval_result)
    
    return evaluation_results


def compute_evaluation_summary(evaluation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Compute summary statistics from evaluation results.
    
    Args:
        evaluation_results: Results from run_evaluation_extraction
        
    Returns:
        Summary statistics dictionary
    """
    if not evaluation_results:
        return {"total": 0}
    
    total = len(evaluation_results)
    evaluation_failures = sum(1 for r in evaluation_results if not r.get("evaluation_success", True))
    
    # Analyze successful evaluations
    successful_evals = [r for r in evaluation_results if r.get("evaluation_success", True) and r.get("evaluation")]
    
    if not successful_evals:
        return {
            "total": total,
            "evaluation_failures": evaluation_failures,
            "evaluated_items": 0,
            "correct_rate": 0.0,
            "formatted_rate": 0.0,
            "both_correct_rate": 0.0
        }
    
    evaluated_count = len(successful_evals)
    correct_count = sum(1 for r in successful_evals if r["evaluation"].get("correct", False))
    formatted_count = sum(1 for r in successful_evals if r["evaluation"].get("formatted", False))
    both_correct_count = sum(1 for r in successful_evals 
                           if r["evaluation"].get("correct", False) and r["evaluation"].get("formatted", False))
    
    # Token usage
    total_input_tokens = sum(r.get("input_tokens", 0) for r in evaluation_results)
    total_output_tokens = sum(r.get("output_tokens", 0) for r in evaluation_results)
    
    # Confidence analysis
    confidences = [r["evaluation"].get("confidence") for r in successful_evals 
                  if r["evaluation"].get("confidence") is not None]
    avg_confidence = sum(confidences) / len(confidences) if confidences else None
    
    return {
        "total": total,
        "evaluation_failures": evaluation_failures,
        "evaluated_items": evaluated_count,
        "correct_count": correct_count,
        "formatted_count": formatted_count,
        "both_correct_count": both_correct_count,
        "correct_rate": correct_count / evaluated_count,
        "formatted_rate": formatted_count / evaluated_count,
        "both_correct_rate": both_correct_count / evaluated_count,
        "avg_confidence": round(avg_confidence, 3) if avg_confidence else None,
        "total_input_tokens": total_input_tokens,
        "total_output_tokens": total_output_tokens,
        "total_tokens": total_input_tokens + total_output_tokens
    }
